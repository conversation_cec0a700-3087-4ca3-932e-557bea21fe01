#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import pdfplumber
import sys

def extract_with_pymupdf(pdf_path, start_page, end_page):
    """Extract text using PyMuPDF (fitz)"""
    print("Extracting with PyMuPDF...")
    doc = fitz.open(pdf_path)
    text_content = []
    
    for page_num in range(start_page - 1, end_page):  # Convert to 0-based indexing
        if page_num < len(doc):
            page = doc.load_page(page_num)
            text = page.get_text()
            text_content.append(f"\n--- Page {page_num + 1} ---\n")
            text_content.append(text)
        else:
            print(f"Page {page_num + 1} not found in document")
    
    doc.close()
    return ''.join(text_content)

def extract_with_pdfplumber(pdf_path, start_page, end_page):
    """Extract text using pdfplumber"""
    print("Extracting with pdfplumber...")
    text_content = []
    
    with pdfplumber.open(pdf_path) as pdf:
        for page_num in range(start_page - 1, end_page):  # Convert to 0-based indexing
            if page_num < len(pdf.pages):
                page = pdf.pages[page_num]
                text = page.extract_text()
                text_content.append(f"\n--- Page {page_num + 1} ---\n")
                text_content.append(text if text else "[No text extracted]")
            else:
                print(f"Page {page_num + 1} not found in document")
    
    return ''.join(text_content)

def main():
    pdf_path = "Seerathe noori final.pdf"
    start_page = 69
    end_page = 88
    
    print(f"Extracting Malayalam text from pages {start_page} to {end_page}")
    print("=" * 50)
    
    # Try PyMuPDF first
    try:
        pymupdf_text = extract_with_pymupdf(pdf_path, start_page, end_page)
        
        # Save PyMuPDF output
        with open("malayalam_text_pymupdf.txt", "w", encoding="utf-8") as f:
            f.write(pymupdf_text)
        print("PyMuPDF extraction saved to 'malayalam_text_pymupdf.txt'")
        
        # Display first few lines to check if it looks correct
        lines = pymupdf_text.split('\n')[:20]
        print("\nFirst 20 lines from PyMuPDF:")
        for line in lines:
            print(line)
            
    except Exception as e:
        print(f"PyMuPDF extraction failed: {e}")
    
    print("\n" + "=" * 50)
    
    # Try pdfplumber
    try:
        pdfplumber_text = extract_with_pdfplumber(pdf_path, start_page, end_page)
        
        # Save pdfplumber output
        with open("malayalam_text_pdfplumber.txt", "w", encoding="utf-8") as f:
            f.write(pdfplumber_text)
        print("pdfplumber extraction saved to 'malayalam_text_pdfplumber.txt'")
        
        # Display first few lines to check if it looks correct
        lines = pdfplumber_text.split('\n')[:20]
        print("\nFirst 20 lines from pdfplumber:")
        for line in lines:
            print(line)
            
    except Exception as e:
        print(f"pdfplumber extraction failed: {e}")

if __name__ == "__main__":
    main()
