#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import re

def create_comprehensive_font_mapping():
    """Create a comprehensive mapping for ML-Revathi font encoding"""
    
    # Basic character mappings for ML-Revathi font
    char_mapping = {
        # Vowels and basic characters
        'A': 'അ',
        'B': 'ബ്',
        'C': 'ക്',
        'D': 'ഡ്',
        'E': 'എ',
        'F': 'ഫ്',
        'G': 'ഗ്',
        'H': 'ഹ്',
        'I': 'ഇ',
        'J': 'ജ്',
        'K': 'ക്',
        'L': 'ൽ',
        'M': 'മ്',
        'N': 'ൻ',
        'O': 'ഓ',
        'P': 'പ്',
        'Q': 'ക്വ്',
        'R': 'ർ',
        'S': 'സ്',
        'T': 'ത്',
        'U': 'ഉ',
        'V': 'വ്',
        'W': 'വ്',
        'X': 'ക്സ്',
        'Y': 'യ്',
        'Z': 'സ്',
        
        # Common combinations
        'k-': 'ക',
        'l-': 'ൽ',
        'm-': 'മ',
        'n-': 'ൻ',
        'p-': 'പ',
        'r-': 'ർ',
        's-': 'സ',
        't-': 'ത',
        'v-': 'വ',
        'w-': 'വ',
        'x-': 'ക്സ',
        'y-': 'യ',
        'z-': 'സ',
        
        # Specific patterns
        'øn': 'ാൻ',
        'Zv': 'സ്',
        '¬': '',
        '≥': 'ൻ',
        '°': 'ോ',
        '±': 'ാ',
        '∂': 'ദ',
        '∏': 'പി',
        '∑': 'സം',
        '∆': 'ഡ',
        '∞': 'ണ',
        '∫': 'ബ',
        '√': 'വ',
        '≈': 'സ',
        '≠': 'ന',
        '≤': 'ക',
        '≥': 'ൻ',
        '¿': 'ര',
        '¡': 'ി',
        '¢': 'ീ',
        '£': 'ു',
        '¤': 'ൂ',
        '¥': 'ൃ',
        '¦': 'െ',
        '§': 'േ',
        '¨': 'ൈ',
        '©': 'ൊ',
        'ª': 'ോ',
        '«': 'ൌ',
        '¬': '',
        '®': 'ർ',
        '¯': 'ൻ',
        '°': 'ൽ',
        '±': 'ാ',
        '²': 'ി',
        '³': 'ീ',
        '´': 'ു',
        'µ': 'ൂ',
        '¶': 'ൃ',
        '·': '്',
        '¸': 'ം',
        '¹': 'ഃ',
        'º': 'ൺ',
        '»': 'ൻ',
        '¼': 'ൽ',
        '½': 'ൾ',
        '¾': 'ൿ',
    }
    
    # Word-level mappings
    word_mapping = {
        # Names and titles
        'k-øn-Zv': 'ഖാൻസ്',
        'A-lv-a-Zv': 'അൽവാസ്',
        'ap-lv-bn±o≥': 'അപ്ലിബ്നാതോൻ',
        '\\q-cn-im': 'ഖുചിം',
        'Po-em-\\n': 'പോയിമൻ',
        'l{k-Øv': 'ലക്ഷ്മി',
        'l-{k-Øv': 'ലക്ഷ്മി',
        'l-{kØv': 'ലക്ഷ്മി',
        'Ku-kp¬': 'കുക്കുപ്പ്',
        'A-Av-fw': 'ആവഫ്',
        'ap-l-Ω-Zv': 'അപ്ലോമസ്',
        'aq-k¬': 'അക്കം',
        'Pu≥': 'പുത്രൻ',
        'A-_v-Zp-√': 'അബ്ദുള്ള',
        'Zm-hq-Zv': 'സാഹിദ്',
        'aq-km': 'അക്കം',
        'b-lv-bm': 'ബിൽബം',
        'km-ln-Zv': 'കമലുദ്ദീൻ',
        'A-_q': 'അബ്ദു',
        'kzm-en-lv': 'കസ്മലിൽ',
        'Pw-Ko-tZm-kv-Xv': 'പുകോതസ്മകവ്',
        'P-∫m¿': 'പങ്കജം',
        'A-_p¬': 'അബുൽ',
        'l-k≥': 'ലക്കം',
        'kz-^n-bp-√m-lv': 'കസ്നിബുള്ളാമിൽ',
        'h-lm-_v': 'ഹലീം',
        'C-{_m-lnw': 'ഇസ്മലിൻ',
        'a-lv-aq-Zv': 'അൽവാസ്',
        ']o-dm≥': 'റഹ്മാൻ',
        'a-Jv-Zpw': 'അജീസും',
        '\\n-km-ap-±o≥': 'നിക്മാപ്പാതോൻ',
        '^-X-lv': 'ഖലീൽ',
        'B-enw': 'ബീനു',
        'Jm-Zn¿': 'ജമാലുദ്ദീൻ',
        
        # Common words
        'ssi-Jp-\\m': 'സിദ്ദീഖ്',
        'A-√m-lp': 'അല്ലാഹ്',
        'd-∫v': 'ദുആ',
        'Jp¿-B≥': 'ഖുർആൻ',
        'hn-izm-kn': 'വിശ്വാസി',
        'a-X-cw-Kw': 'മതമേഖല',
        'Po-hn-Xw': 'ജീവിതം',
        'B-ﬂo-b': 'ആത്മീയ',
        'tk-h-\\w': 'സേവനം',
        'hn-⁄m-\\w': 'വിദ്യാഭ്യാസം',
        'Pm-an-A': 'മദ്രസ',
        'hn-Zym¿-∞n-Iƒ': 'വിദ്യാർത്ഥികൾ',
        'Kp-cp': 'ഗുരു',
        'in-jy': 'ശിഷ്യ',
        'a-lm-\\m': 'ആലിം',
        ']ﬁn-X≥': 'പണ്ഡിതൻ',
        'ssl-Z-cm-_m-Zv': 'സിലസിലാമാസ്',
        'tI-c-fw': 'കേരളം',
        'C-¥y': 'ഇന്ത്യ',
        
        # Family relations
        ']n-Xm-hv': 'പിതാവ്',
        'am-Xm-hv': 'മാതാവ്',
        ']p-{X≥': 'പുത്രൻ',
        ']p-{Xn': 'പുത്രി',
        'a-°ƒ': 'മക്കൾ',
        'Ip-Spw-_w': 'കുടുംബം',
        
        # Time and dates
        'ln-Pv-d': 'ഹിജ്റ',
        'Zp¬-J-Av-Zv': 'സഫർ',
        '{In.': 'ക്രി.',
        'H-Iv-tSm-_¿': 'ഒക്ടോബർ',
        
        # Numbers
        'C-cp-]': 'ഇരുപത്',
        '\\m-ev': 'നാല്',
        'A-©v': 'അഞ്ച്',
        'B-dm-a': 'ആറാമത്',
        
        # Remove markers
        '(d)': '',
        '(J:kn)': '(ജ:കൻ)',
        
        # Common suffixes
        '-bm-Wv': 'ആണ്',
        '-bn¬': 'ൽ',
        '-te-°v': 'ലേക്ക്',
        '-Øn¬': 'ത്തിൽ',
        '-s‚': 'ന്റെ',
        '-cpw': 'ഉം',
        '-fpw': 'ഉം',
    }
    
    return char_mapping, word_mapping

def advanced_decode_text(text):
    """Advanced decoding using both character and word mappings"""
    char_mapping, word_mapping = create_comprehensive_font_mapping()
    
    # First apply word-level mappings
    decoded_text = text
    for encoded, malayalam in word_mapping.items():
        decoded_text = decoded_text.replace(encoded, malayalam)
    
    # Then apply character-level mappings for remaining encoded parts
    for encoded, malayalam in char_mapping.items():
        decoded_text = decoded_text.replace(encoded, malayalam)
    
    # Clean up extra spaces and formatting
    decoded_text = re.sub(r'\s+', ' ', decoded_text)
    decoded_text = decoded_text.strip()
    
    return decoded_text

def extract_and_decode_enhanced(pdf_path, start_page, end_page):
    """Enhanced extraction and decoding"""
    print(f"Enhanced extraction from pages {start_page} to {end_page}")
    print("=" * 60)
    
    doc = fitz.open(pdf_path)
    decoded_content = []
    
    for page_num in range(start_page - 1, min(end_page, len(doc))):
        page = doc.load_page(page_num)
        
        # Extract text
        text = page.get_text()
        
        decoded_content.append(f"\n--- Page {page_num + 1} (Enhanced Decode) ---\n")
        
        if text.strip():
            # Split into lines and decode each line
            lines = text.split('\n')
            decoded_lines = []
            
            for line in lines:
                if line.strip():
                    decoded_line = advanced_decode_text(line)
                    decoded_lines.append(decoded_line)
            
            if decoded_lines:
                decoded_content.append('\n'.join(decoded_lines))
            else:
                decoded_content.append("[No text found]")
        else:
            decoded_content.append("[No text found]")
    
    doc.close()
    return '\n'.join(decoded_content)

def main():
    pdf_path = "Seerathe noori final.pdf"
    start_page = 75  # Test with the specific page you mentioned
    end_page = 76
    
    try:
        decoded_text = extract_and_decode_enhanced(pdf_path, start_page, end_page)
        
        # Save enhanced decoded text
        with open("malayalam_text_enhanced.txt", "w", encoding="utf-8") as f:
            f.write(decoded_text)
        
        print(f"\nEnhanced decoded text saved to 'malayalam_text_enhanced.txt'")
        
        # Display sample
        print("\nSample enhanced decoded text:")
        print("=" * 50)
        lines = decoded_text.split('\n')[:50]
        for line in lines:
            if line.strip():
                print(line)
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
