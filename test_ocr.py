#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import easyocr
import os
import glob

def test_ocr_on_images():
    """Test OCR on the generated images"""
    print("Testing OCR on generated images...")

    # Check supported languages first
    print("Checking supported languages...")
    try:
        # Try different language codes for Malayalam/Devanagari
        supported_langs = ['en', 'hi', 'ta', 'te', 'kn']  # English, Hindi, Tamil, Telugu, Kannada
        print(f"Trying languages: {supported_langs}")
        reader = easyocr.Reader(supported_langs)
        print("EasyOCR reader initialized successfully!")
    except Exception as e:
        print(f"Error with multiple languages: {e}")
        print("Falling back to English only...")
        reader = easyocr.Reader(['en'])
        print("EasyOCR reader initialized with English only!")
    
    # Get all image files
    image_folder = "pdf_images"
    image_files = sorted(glob.glob(os.path.join(image_folder, "page_*.png")))
    
    if not image_files:
        print("No image files found!")
        return
    
    print(f"Found {len(image_files)} image files")
    
    # Test on first few pages
    test_files = image_files[:3]  # Test on first 3 pages
    
    all_text = []
    
    for image_path in test_files:
        page_num = os.path.basename(image_path).split('_')[1].split('.')[0]
        print(f"\nProcessing page {page_num}...")
        
        try:
            # Extract text from image
            results = reader.readtext(image_path)
            
            print(f"Found {len(results)} text regions")
            
            page_text = [f"\n--- Page {page_num} (OCR) ---\n"]
            
            # Combine all detected text
            for i, (bbox, text, confidence) in enumerate(results):
                print(f"  Region {i+1}: '{text}' (confidence: {confidence:.2f})")
                if confidence > 0.3:  # Filter out low-confidence detections
                    page_text.append(text)
            
            if len(page_text) > 1:  # More than just the header
                all_text.extend(page_text)
                print(f"Extracted {len(page_text)-1} text regions from page {page_num}")
            else:
                all_text.append(f"\n--- Page {page_num} (OCR) ---\n[No text detected]\n")
                print(f"No text detected on page {page_num}")
                
        except Exception as e:
            print(f"Error processing page {page_num}: {e}")
            all_text.append(f"\n--- Page {page_num} (OCR) ---\n[Error: {e}]\n")
    
    # Save results
    if all_text:
        output_text = '\n'.join(all_text)
        with open("malayalam_text_ocr_test.txt", "w", encoding="utf-8") as f:
            f.write(output_text)
        print(f"\nOCR test results saved to 'malayalam_text_ocr_test.txt'")
        
        # Display sample
        print("\nSample OCR output:")
        print("=" * 50)
        lines = output_text.split('\n')[:30]
        for line in lines:
            if line.strip():
                print(line)
    else:
        print("No text extracted from any page")

if __name__ == "__main__":
    test_ocr_on_images()
