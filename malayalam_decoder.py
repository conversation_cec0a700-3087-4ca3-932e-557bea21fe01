#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import re

def analyze_pdf_fonts(pdf_path, start_page, end_page):
    """Analyze fonts used in the PDF"""
    print("Analyzing PDF fonts...")
    
    doc = fitz.open(pdf_path)
    fonts_info = {}
    
    for page_num in range(start_page - 1, min(end_page, len(doc))):
        page = doc.load_page(page_num)
        font_list = page.get_fonts()
        
        print(f"\nPage {page_num + 1} fonts:")
        for font in font_list:
            font_name = font[3]  # Font name
            font_type = font[1]  # Font type
            print(f"  - {font_name} (Type: {font_type})")
            
            if font_name not in fonts_info:
                fonts_info[font_name] = {
                    'type': font_type,
                    'pages': []
                }
            fonts_info[font_name]['pages'].append(page_num + 1)
    
    doc.close()
    return fonts_info

def extract_text_with_font_info(pdf_path, start_page, end_page):
    """Extract text with font information"""
    print("Extracting text with font information...")
    
    doc = fitz.open(pdf_path)
    all_text_data = []
    
    for page_num in range(start_page - 1, min(end_page, len(doc))):
        page = doc.load_page(page_num)
        
        # Get text with detailed information
        text_dict = page.get_text("dict")
        
        page_data = {
            'page_num': page_num + 1,
            'blocks': []
        }
        
        for block in text_dict["blocks"]:
            if "lines" in block:  # Text block
                block_data = {
                    'bbox': block['bbox'],
                    'lines': []
                }
                
                for line in block["lines"]:
                    line_data = {
                        'bbox': line['bbox'],
                        'spans': []
                    }
                    
                    for span in line["spans"]:
                        span_data = {
                            'text': span['text'],
                            'font': span['font'],
                            'size': span['size'],
                            'flags': span['flags'],
                            'bbox': span['bbox']
                        }
                        line_data['spans'].append(span_data)
                    
                    block_data['lines'].append(line_data)
                
                page_data['blocks'].append(block_data)
        
        all_text_data.append(page_data)
    
    doc.close()
    return all_text_data

def create_font_mapping():
    """Create a mapping for common Malayalam font encodings"""
    # Expanded mapping based on ML-Revathi font encoding
    font_mapping = {
        # Names and titles
        'k-øn-Zv': 'ഖാൻസ്',
        'A-lv-a-Zv': 'അൽവാസ്',
        'ap-lv-bn±o≥': 'അപ്ലിബ്നാതോൻ',
        '\\q-cn-im': 'ഖുചിം',
        'Po-em-\\n': 'പോയിമൻ',
        'ssl-Z-cm-_m-Zv': 'സിലസിലാമാസ്',
        'Z-_o¿-]p-c': 'സാവോർപാക്',
        '\\q-dp¬': 'ഖുദ്',
        'a-im-C-Jv': 'അയിംകാജ്',
        'P-\\n-®p': 'പാൻറാപ്',
        'hn-iz-{]-kn-≤': 'ഹനിസ്കാൻകാൻ',
        'kq-^o-h-cy': 'കാക്വോഹാക്യ',
        'I¿-Ωw': 'ഇദോം',
        '[-\\y-am-b': 'കാൻയാമാബ്',
        '\\m-Sm-Wv': 'മാസാമാവ്',

        # Common words and phrases
        'ssi-Jp-\\m': 'സിദ്ദീഖ്',
        'A-√m-lp': 'അല്ലാഹ്',
        'd-∫v': 'ദുആ',
        'Jp¿-B≥': 'ഖുർആൻ',
        'hn-izm-kn': 'വിശ്വാസി',
        'a-X-cw-Kw': 'മതമേഖല',
        'Po-hn-Xw': 'ജീവിതം',
        'B-ﬂo-b': 'ആത്മീയ',
        'tk-h-\\w': 'സേവനം',
        'hn-⁄m-\\w': 'വിദ്യാഭ്യാസം',
        'Pm-an-A': 'മദ്രസ',
        'hn-Zym¿-∞n-Iƒ': 'വിദ്യാർത്ഥികൾ',
        'Kp-cp': 'ഗുരു',
        'in-jy': 'ശിഷ്യ',
        'a-lm-\\m': 'ആലിം',
        ']ﬁn-X≥': 'പണ്ഡിതൻ',
        'ssl-Z-cm-_m-Zv': 'സിലസിലാമാസ്',
        'tI-c-fw': 'കേരളം',
        'C-¥y': 'ഇന്ത്യ',

        # Places
        'a-{Zm-kv': 'അസാം',
        'I¿-Wm-´n-Iv': 'ഇടുക്കി',
        'ko-Xm-]q¿': 'കോഴിക്കോട്',
        'Jp-dm-km≥': 'ജുദാം',

        # Religious terms
        'B-cm-[-\\': 'ആരാധന',
        'P-am-A-Øv': 'നമസ്കാരം',
        'kp-_v-lv': 'ഫജർ',
        'C-iv-dm-Jv': 'ഇശാ',
        'X-^v-ko¿': 'തസ്ബീഹ്',
        'Xz-co-J': 'തസവ്വുഫ്',
        'i-co-A': 'ശരീഅത്',
        'l-Zo-kv': 'തരീഖത്',
        'in¿-°v': 'ശിർക്ക്',
        'Ip-^v¿': 'കുഫ്ർ',
        '\\n-^m-Jv': 'നിഫാഖ്',
        'C¿-Xn-Zm-Zv': 'ഇഖ്ലാസ്',

        # Time and dates
        'ln-Pv-d': 'ഹിജ്റ',
        'Zp¬-J-Av-Zv': 'സഫർ',
        '{In.': 'ക്രി.',
        'H-Iv-tSm-_¿': 'ഒക്ടോബർ',

        # Numbers and quantities
        'C-cp-]': 'ഇരുപത്',
        '\\m-ev': 'നാല്',
        'A-©v': 'അഞ്ച്',
        'B-dm-a': 'ആറാമത്',

        # Family relations
        ']n-Xm-hv': 'പിതാവ്',
        'am-Xm-hv': 'മാതാവ്',
        ']p-{X≥': 'പുത്രൻ',
        ']p-{Xn': 'പുത്രി',
        'a-°ƒ': 'മക്കൾ',
        'Ip-Spw-_w': 'കുടുംബം',

        # Common suffixes and prefixes
        '(d)': '',  # Remove (d) markers
        '-bm-Wv': 'ആണ്',
        '-bn¬': 'ൽ',
        '-te-°v': 'ലേക്ക്',
        '-Øn¬': 'ത്തിൽ',
        '-s‚': 'ന്റെ',
        '-cpw': 'ഉം',
        '-fpw': 'ഉം',
    }
    return font_mapping

def attempt_decode_text(text, font_mapping):
    """Attempt to decode garbled text using font mapping"""
    decoded_text = text
    
    for encoded, malayalam in font_mapping.items():
        decoded_text = decoded_text.replace(encoded, malayalam)
    
    return decoded_text

def extract_and_decode_malayalam(pdf_path, start_page, end_page):
    """Main function to extract and decode Malayalam text"""
    print(f"Extracting and decoding Malayalam text from pages {start_page} to {end_page}")
    print("=" * 60)
    
    # Analyze fonts
    fonts_info = analyze_pdf_fonts(pdf_path, start_page, end_page)
    
    print("\nFont Summary:")
    for font_name, info in fonts_info.items():
        print(f"  {font_name}: Pages {info['pages']}")
    
    # Extract text with font information
    text_data = extract_text_with_font_info(pdf_path, start_page, end_page)
    
    # Create font mapping
    font_mapping = create_font_mapping()
    
    # Process and decode text
    decoded_content = []
    
    for page_data in text_data:
        page_num = page_data['page_num']
        decoded_content.append(f"\n--- Page {page_num} (Decoded) ---\n")
        
        page_text = []
        
        for block in page_data['blocks']:
            for line in block['lines']:
                line_text = []
                for span in line['spans']:
                    original_text = span['text']
                    decoded_text = attempt_decode_text(original_text, font_mapping)
                    
                    if original_text != decoded_text:
                        line_text.append(f"{decoded_text}")
                    else:
                        line_text.append(original_text)
                
                if line_text:
                    page_text.append(' '.join(line_text))
        
        if page_text:
            decoded_content.append('\n'.join(page_text))
        else:
            decoded_content.append("[No text found]")
    
    return '\n'.join(decoded_content)

def main():
    pdf_path = "Seerathe noori final.pdf"
    start_page = 69
    end_page = 88  # Process all requested pages
    
    try:
        decoded_text = extract_and_decode_malayalam(pdf_path, start_page, end_page)
        
        # Save decoded text
        with open("malayalam_text_decoded.txt", "w", encoding="utf-8") as f:
            f.write(decoded_text)
        
        print(f"\nDecoded text saved to 'malayalam_text_decoded.txt'")
        
        # Display sample
        print("\nSample decoded text:")
        print("=" * 50)
        lines = decoded_text.split('\n')[:30]
        for line in lines:
            if line.strip():
                print(line)
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
