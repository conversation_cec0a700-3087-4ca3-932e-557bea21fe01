#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import pdfplumber
import sys
import os
from pdf2image import convert_from_path
from PIL import Image

def extract_with_pymupdf(pdf_path, start_page, end_page):
    """Extract text using PyMuPDF (fitz)"""
    print("Extracting with PyMuPDF...")
    doc = fitz.open(pdf_path)
    text_content = []

    for page_num in range(start_page - 1, end_page):  # Convert to 0-based indexing
        if page_num < len(doc):
            page = doc.load_page(page_num)
            text = page.get_text()
            text_content.append(f"\n--- Page {page_num + 1} ---\n")
            text_content.append(text)
        else:
            print(f"Page {page_num + 1} not found in document")

    doc.close()
    return ''.join(text_content)

def extract_with_pdfplumber(pdf_path, start_page, end_page):
    """Extract text using pdfplumber"""
    print("Extracting with pdfplumber...")
    text_content = []

    with pdfplumber.open(pdf_path) as pdf:
        for page_num in range(start_page - 1, end_page):  # Convert to 0-based indexing
            if page_num < len(pdf.pages):
                page = pdf.pages[page_num]
                text = page.extract_text()
                text_content.append(f"\n--- Page {page_num + 1} ---\n")
                text_content.append(text if text else "[No text extracted]")
            else:
                print(f"Page {page_num + 1} not found in document")

    return ''.join(text_content)

def convert_pdf_to_images_pymupdf(pdf_path, start_page, end_page, output_folder="pdf_images"):
    """Convert PDF pages to images using PyMuPDF"""
    print("Converting PDF pages to images using PyMuPDF...")

    # Create output folder if it doesn't exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    try:
        doc = fitz.open(pdf_path)
        image_paths = []

        for page_num in range(start_page - 1, end_page):  # Convert to 0-based indexing
            if page_num < len(doc):
                page = doc.load_page(page_num)

                # Convert page to image with high resolution
                mat = fitz.Matrix(3.0, 3.0)  # 3x zoom for better quality
                pix = page.get_pixmap(matrix=mat)

                # Save as PNG
                image_path = os.path.join(output_folder, f"page_{page_num + 1}.png")
                pix.save(image_path)
                image_paths.append(image_path)
                print(f"Saved page {page_num + 1} as {image_path}")
            else:
                print(f"Page {page_num + 1} not found in document")

        doc.close()
        return image_paths

    except Exception as e:
        print(f"Error converting PDF to images: {e}")
        return []

def convert_pdf_to_images(pdf_path, start_page, end_page, output_folder="pdf_images"):
    """Convert PDF pages to images for OCR - fallback method"""
    try:
        # Try pdf2image first
        from pdf2image import convert_from_path
        print("Converting PDF pages to images using pdf2image...")

        # Create output folder if it doesn't exist
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        # Convert specific pages to images
        images = convert_from_path(
            pdf_path,
            first_page=start_page,
            last_page=end_page,
            dpi=300,  # High DPI for better OCR
            fmt='PNG'
        )

        image_paths = []
        for i, image in enumerate(images):
            page_num = start_page + i
            image_path = os.path.join(output_folder, f"page_{page_num}.png")
            image.save(image_path, 'PNG')
            image_paths.append(image_path)
            print(f"Saved page {page_num} as {image_path}")

        return image_paths

    except Exception as e:
        print(f"pdf2image failed: {e}")
        print("Falling back to PyMuPDF method...")
        return convert_pdf_to_images_pymupdf(pdf_path, start_page, end_page, output_folder)

def extract_with_ocr_easyocr(image_paths):
    """Extract text using EasyOCR (supports Malayalam)"""
    try:
        import easyocr
        print("Extracting text using EasyOCR...")

        # Initialize EasyOCR reader with Malayalam and English
        reader = easyocr.Reader(['ml', 'en'])  # 'ml' for Malayalam

        text_content = []
        for image_path in image_paths:
            page_num = os.path.basename(image_path).split('_')[1].split('.')[0]
            print(f"Processing page {page_num}...")

            # Extract text from image
            results = reader.readtext(image_path)

            text_content.append(f"\n--- Page {page_num} (OCR) ---\n")

            # Combine all detected text
            page_text = []
            for (bbox, text, confidence) in results:
                if confidence > 0.3:  # Filter out low-confidence detections
                    page_text.append(text)

            if page_text:
                text_content.append('\n'.join(page_text))
            else:
                text_content.append("[No text detected]")

        return ''.join(text_content)

    except ImportError:
        print("EasyOCR not installed. Installing...")
        os.system("pip3 install easyocr")
        return extract_with_ocr_easyocr(image_paths)
    except Exception as e:
        print(f"Error with EasyOCR: {e}")
        return ""

def main():
    pdf_path = "Seerathe noori final.pdf"
    start_page = 69
    end_page = 88

    print(f"Extracting Malayalam text from pages {start_page} to {end_page}")
    print("=" * 50)

    # Try PyMuPDF first
    try:
        pymupdf_text = extract_with_pymupdf(pdf_path, start_page, end_page)

        # Save PyMuPDF output
        with open("malayalam_text_pymupdf.txt", "w", encoding="utf-8") as f:
            f.write(pymupdf_text)
        print("PyMuPDF extraction saved to 'malayalam_text_pymupdf.txt'")

        # Display first few lines to check if it looks correct
        lines = pymupdf_text.split('\n')[:10]
        print("\nFirst 10 lines from PyMuPDF:")
        for line in lines:
            print(line)

    except Exception as e:
        print(f"PyMuPDF extraction failed: {e}")

    print("\n" + "=" * 50)

    # Try pdfplumber
    try:
        pdfplumber_text = extract_with_pdfplumber(pdf_path, start_page, end_page)

        # Save pdfplumber output
        with open("malayalam_text_pdfplumber.txt", "w", encoding="utf-8") as f:
            f.write(pdfplumber_text)
        print("pdfplumber extraction saved to 'malayalam_text_pdfplumber.txt'")

        # Display first few lines to check if it looks correct
        lines = pdfplumber_text.split('\n')[:10]
        print("\nFirst 10 lines from pdfplumber:")
        for line in lines:
            print(line)

    except Exception as e:
        print(f"pdfplumber extraction failed: {e}")

    print("\n" + "=" * 50)

    # Try OCR method
    try:
        print("Attempting OCR extraction...")

        # Convert PDF pages to images
        image_paths = convert_pdf_to_images(pdf_path, start_page, end_page)

        if image_paths:
            print(f"Successfully converted {len(image_paths)} pages to images")

            # Extract text using OCR
            ocr_text = extract_with_ocr_easyocr(image_paths)

            if ocr_text:
                # Save OCR output
                with open("malayalam_text_ocr.txt", "w", encoding="utf-8") as f:
                    f.write(ocr_text)
                print("OCR extraction saved to 'malayalam_text_ocr.txt'")

                # Display first few lines to check if it looks correct
                lines = ocr_text.split('\n')[:20]
                print("\nFirst 20 lines from OCR:")
                for line in lines:
                    if line.strip():  # Only print non-empty lines
                        print(line)
            else:
                print("OCR extraction returned no text")
        else:
            print("Failed to convert PDF pages to images")

    except Exception as e:
        print(f"OCR extraction failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
